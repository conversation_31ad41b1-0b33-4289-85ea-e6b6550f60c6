// 📁 app/_layout.tsx (expo-router)
import { HapticTab } from '@/components/HapticTab';
import SideMenu from '@/components/menu';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import type { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { Tabs } from 'expo-router';
import React, { useState } from 'react';
import { Platform, Text, TouchableOpacity } from 'react-native';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const [menuVisible, setMenuVisible] = useState(false);

  const renderPlusTabBarButton = (props: BottomTabBarButtonProps) => {
    const selected = props.accessibilityState?.selected ?? false;
    return (
      <TouchableOpacity
        
        onPress={() => setMenuVisible(true)}
        style={{ flex: 1, alignItems: 'center', justifyContent: 'center' , padding: 10, }}
      >
        <Ionicons name="menu" size={20} color={selected ? Colors.blue.tint : 'white'} />
        <Text style={{ color: selected ? Colors.blue.tint : 'white', fontSize: 12 }}>Plus</Text>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarButton: HapticTab,
          tabBarBackground: TabBarBackground,
          tabBarStyle: Platform.select({
            ios: {position: 'absolute', height: 90 },
            default: { backgroundColor: '#020617', height: 90 },
          }),
          tabBarActiveTintColor: Colors.blue.tint,
          tabBarInactiveTintColor: '#fff',
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: 'Accueil',
            tabBarIcon: ({ color }) => <FontAwesome5 name="home" size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="compte"
          options={{
            title: 'Compte',
            tabBarIcon: ({ color }) => <Ionicons name="wallet" size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="credit"
          options={{
            title: 'Crédit',
            tabBarIcon: ({ color }) => <Ionicons name="card" size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="plus"
          options={{
            tabBarButton: renderPlusTabBarButton,
          }}
        />
        <Tabs.Screen
        name="notification"
        options={{
          href:null,
          title: 'Notification',
          // tabBarIcon: ({ color }) => <Ionicons name="menu" size={20} color={color} />,
        }}
      />
      <Tabs.Screen
        name="historique"
        options={{
          href:null,
          title: 'historique',
          // tabBarIcon: ({ color }) => <Ionicons name="menu" size={20} color={color} />,
        }}
      />
      <Tabs.Screen
        name="transaction"
        options={{
          href:null,
          title: 'Transaction',}}/>
        <Tabs.Screen
        name="epargne"
        options={{
          href:null,
          title: 'Epargne',
          // tabBarIcon: ({ color }) => <Ionicons name="menu" size={20} color={color} />,
        }}
      />
       {/*  <Tabs.Screen
        name="login"
        options={{
          headerShown: false,
          href:null,
          title: 'Login',
          // tabBarIcon: ({ color }) => <Ionicons name="menu" size={20} color={color} />,
        }}
      /> */}
      </Tabs>

      <SideMenu visible={menuVisible} onClose={() => setMenuVisible(false)} />
    </>
  );
}
