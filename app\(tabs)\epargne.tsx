import { Colors } from '@/constants/Colors';
import React,{ useState } from 'react';
import { FontAwesome5, Ionicons } from '@expo/vector-icons'; 
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View, } from 'react-native'; 
 
import { router } from 'expo-router';

const screenWidth = Dimensions.get('window').width;
 
 
 

 

export default function TabTwoScreen() {
  const [showText, setShowText] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [carte, setCarte] = useState(1); 

 
 
const redirectToCompte = (url: any) => {
  router.push(url); // ✅
};

  return (
  <View style={styles.containerFluid}>
    {/* Header */}
    <View style={styles.header}> 
      <TouchableOpacity onPress={() => {router.back();}}>
        <FontAwesome5 name="chevron-left" size={20} color={Colors.blue.text} /> 
      </TouchableOpacity>
      <Text style={styles.headerTitle}>
        Epargne (EAC)
        <View style={styles.roundeNbr}>
          <Text style={{color: Colors.blue.text, fontSize: 10, fontWeight: '700'}}>{carte}</Text>
        </View>
      </Text> 
      <TouchableOpacity onPress={() => setShowPopup(!showPopup)} style={styles.cards}>
        <Ionicons name="card" size={20} color={Colors.blue.text} style={styles.card1}/>
        <Ionicons name="card" size={20} color={"white"} style={styles.card2}/>
      </TouchableOpacity>
    </View> 

    <ScrollView style={styles.container}  >
         

          {/* solde disponible */}
          <View style={{gap: 20, padding: 24, margin:16, backgroundColor: Colors.dark.slate900}}> 
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: Colors.dark.icon, fontSize:13, fontWeight: 'normal'}}>Nombre de cycle en cour :</Text> 
                <Text style={{color: '#34d399', fontSize: 14, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  0
                </Text> 
            </View> 
            <View style={{height:1,backgroundColor:'rgba(209, 213, 225, 0.1)'}}></View>
             
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: Colors.dark.icon, fontSize:15, fontWeight: 'normal'}}>Montant total de cycle :</Text> 
                <Text style={{color: '#34d399', fontSize: 14, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  0
                </Text> 
            </View> 
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: Colors.dark.icon, fontSize:15, fontWeight: 'normal'}}>Montant total Mise :</Text> 
                <Text style={{color: '#34d399', fontSize: 14, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  0
                </Text> 
          </View>
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: Colors.dark.icon, fontSize:15, fontWeight: 'normal'}}>Montant   Mise :</Text> 
                <Text style={{color:  '#34d399', fontSize: 14, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  0
                </Text> 
            </View>
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: Colors.dark.icon, fontSize:15, fontWeight: 'normal'}}>Devise :</Text> 
                <Text style={{color:  '#34d399', fontSize: 14, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  USD
                </Text> 
            </View>
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: Colors.dark.icon, fontSize:15, fontWeight: 'normal'}}>Nombre Mise :</Text> 
                <Text style={{color: '#34d399', fontSize: 14, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  0
                </Text> 
            </View>
          </View>

          <View style={{height:1,backgroundColor:'rgba(209, 213, 225, 0.1)'}}></View>

          <View style={{gap: 20, padding: 16}}> 
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: 'white', fontSize:13, fontWeight: 'normal'}}>Date</Text> 
                <Text style={{color: 'white', fontSize: 16, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  Mardi, Le 14/06/2025
                </Text> 
            </View>  
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: 'white', fontSize:13, fontWeight: 'normal'}}>Type</Text> 
                <Text style={{color: 'white', fontSize: 16, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  Inconnue
                </Text> 
            </View>  
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: 'white', fontSize:13, fontWeight: 'normal'}}>Montant</Text> 
                <Text style={{color: 'white', fontSize: 16, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  0
                </Text> 
            </View>
            <View style={{flexDirection:'row', gap:16, alignItems: 'center',justifyContent:'space-between'}}>
              <Text style={{color: 'white', fontSize:13, fontWeight: 'normal'}}>Devise</Text> 
                <Text style={{color: 'white', fontSize: 16, fontWeight: 'bold', paddingVertical: 5,  paddingHorizontal: 10, borderRadius: 5, backgroundColor: '#1f2937'}}>
                  CDF
                </Text> 
            </View>    
            </View> 


      
</ScrollView>
    {/* Popup */}
    {showPopup && (
      <View style={styles.popup}>
        <View style={styles.popupContent}>
          <Text style={styles.popupText}>Changer de carte</Text>
          <View style={{gap: 10}}>
            <TouchableOpacity style={styles.popupButton} onPress={() => {setShowPopup(false); setCarte(1);}}>
              <Text style={styles.popupButtonText}>Carte 1</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.popupButton} onPress={() => {setShowPopup(false); setCarte(2);}}>
              <Text style={styles.popupButtonText}>Carte 2</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity onPress={() => setShowPopup(false)} style={styles.popupButton2}>
            <Text style={styles.popupButtonText}>Fermer</Text>
          </TouchableOpacity>
        </View>
      </View>
    )}
  </View>
);

}

const trucanteCode = (data: string): string => {
  const subCode = data.slice(data.length - 4, data.length);
  return `*****${subCode}`;
};

const styles = StyleSheet.create({
   containerFluid: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    marginTop: 54,
    paddingTop: 16,
  },
  header: {
    backgroundColor: Colors.dark.slate800,
    position: 'sticky',
    top: 54,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    zIndex: 1099,
    height: 54,
  }, 
  headerTitle: {
    color: 'white',
    fontSize: 18,
    position: 'relative',
    flexDirection: 'row'

  }, 
  cards: {
    position: 'relative', 
  },  
  card1: {
    marginRight: 10,
  },
  card2: {
    position: 'absolute',
    left: 10,
    top: -5,
  },
  comptes: {
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  lineTrough: {
    width: 250,
    backgroundColor: 'rgba(209, 213, 225, 0.1)',
    height: 320,
    position: 'absolute',
    top: -100,
    left: 160,
    transform: [{ rotate: '-35deg' }],
    zIndex: -111,
  },
  comptesCard: {
    backgroundColor: Colors.blue.blueTwo,
    overflow: 'hidden', 
    borderRadius: 12,
    padding: 16,
    height: 200,
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 333,
    marginTop: 60,
  },
  comptesNom: {
    color: '#7B4019',
    fontWeight: 'bold',
    fontSize: 16,
  },
  comptesDetail: {
    color: 'white',
    fontSize: 24,
    marginTop: 4,
  },
  ownerProp: {
    color: Colors.dark.icon,
  },
  roundeNbr: {
    backgroundColor: 'white',  
    borderRadius: 50, 
    position: 'relative',
    top: -2,
    left: 12,
    zIndex: 999,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center', 
  },
  transItem: {
    backgroundColor: '#1f2937',  
    padding: 16,
    borderRadius: 16,
    marginBottom: 8,
  },
  transLabel: {
    color: 'white',
    fontSize: 16,
  },
  transDate: {
    color: '#9ca3af', 
    fontSize: 12,
  },
  transMontant: {
    color: '#34d399',  
    fontSize: 12,
  },
  toggleExpand: {
    marginBottom: 8,
  },
  toggleText: {
    textAlign: 'center',
    color: '#9ca3af',
  },
  title: {
  fontSize: 18,
  color: 'white',
  fontWeight: 'bold',
  marginBottom: 12,
},

  title2: {flexDirection:'row', alignItems: 'center', color: 'white', fontSize: 18, fontWeight: 'bold', marginBottom: 12,gap:12,
  },
actionButton: {
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: Colors.dark.slate700,
  paddingVertical: 32,
  paddingHorizontal: 12,
  borderRadius: 10,
  marginBottom: 12,
  gap: 10,
},
actionButton2: {
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: Colors.dark.slate700,
  paddingVertical: 32,
  paddingHorizontal: 12,
  borderRadius: 10,
  marginBottom: 12,
  gap: 10,
},
actionText: {
  color: 'white',
  fontSize: 16,
},
detailBox: {
  marginBottom: 10,
  backgroundColor: Colors.dark.slate700,
  padding: 12,
  borderRadius: 10,
},
label: {
  color: '#9ca3af',
  fontSize: 14,
},
value: {
  color: 'white',
  fontSize: 16,
  fontWeight: '600',
},
popup: {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 2222
},
popupContent: {
  backgroundColor: Colors.dark.slate700,
  minHeight: 350,
  minWidth: 250,
  padding: 20,
  gap:8,
  borderRadius: 12,
  borderLeftWidth: 1,
  borderColor: Colors.dark.slate800,
},
popupText: {
  fontSize: 16,
  marginBottom: 20,
  color: 'white',
},
popupButton: {
  backgroundColor: '#1e3a8a22',
  paddingVertical: 15,
  paddingHorizontal: 20,
  borderBottomWidth: 1,
  borderBottomColor: Colors.dark.slate800,
  borderRadius: 5,
},
popupButton2: {
   position: 'absolute',
   bottom: 0, 
   right: 0, 
   paddingVertical: 15,
   paddingHorizontal: 20,
   borderRadius: 5,
},
popupButtonText: {
  color: 'white',
  fontSize: 16,
},

});
