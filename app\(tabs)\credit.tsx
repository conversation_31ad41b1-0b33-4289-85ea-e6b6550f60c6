import { Colors } from '@/constants/Colors';
import { FontAwesome5, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Link, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const screenWidth = Dimensions.get('window').width;
export default function TabThreeScreen() {
  let [selectCart,setSelectCart]=useState(false)
  let [compteValue,setCompteValue]=useState('107265')
  const router=useRouter()
  const closeSwitchCompte=(value:string)=>{
    setSelectCart(!selectCart)
    setCompteValue(value)
  }
  let [focusRemboussement,setFocusRembourssement]=useState(0)
  return (
    <View style={styles.containerFluid}>
       {/* Header */}
      <View style={styles.header}>
        <View>
          <TouchableOpacity onPress={() => {router.back();}}>
                   <FontAwesome5 name="chevron-left" size={20} color={Colors.blue.text} /> 
                 </TouchableOpacity>
        </View>
        <Text style={styles.headerTitle}>Crédit</Text>
        <TouchableOpacity onPress={()=>router.push('/(tabs)/notification')}>
          <Ionicons name="notifications-outline" size={24} color="white" style={styles.notifIcon} />
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.container}>
      {/* prets */}
      <View>
        <Text style={{color:'#fefefe',fontSize:32,marginVertical:20}}>Mes prêts</Text>
        {/* carte pret */}
       <View style={styles.comptesCard}>
          <View style={styles.lineTrough}></View>
          <View>
            <View style={{justifyContent:'space-between',flexDirection:'row',alignItems:'center'}}>
              <Text style={styles.comptesDetail}>Compte de prêts</Text> 
              <Text style={{color:Colors.dark.slate700}}>
                <Ionicons name="ellipsis-horizontal" size={24} color="black" />
              </Text>
            </View>
             <TouchableOpacity onPress={()=>{setSelectCart(!selectCart)}}>
                <View style={{flexDirection:'row',gap:5,marginTop:10}}>
                  <Text>{compteValue}</Text>
                  <FontAwesome5 name={selectCart ? 'chevron-up' : 'chevron-down'} size={16} color={Colors.dark.slate700} />
                </View>
              </TouchableOpacity>
              {selectCart && (
                  <View style={styles.cardChoise}>
                    <TouchableOpacity onPress={()=>closeSwitchCompte('107265')}>
                      <Text style={styles.compteItems}>107265</Text>
                    </TouchableOpacity>
                     <TouchableOpacity onPress={()=>closeSwitchCompte('108889')}>
                      <Text style={styles.compteItems}>108889</Text>
                    </TouchableOpacity>
                  </View>
                )}
          </View>
          {/* bottom progress */}
          <View style={{justifyContent:'space-between',gap:5}}>
            <View style={{marginHorizontal:10}}>
            {/* <Text style={styles.comptesDetail}>066200039657052 • Compte courant</Text> */}
              <View style={{justifyContent:'space-between',alignItems:'center',flexDirection:'row',marginBottom:10}}>
                <Text style={{color:'rgba(30, 41, 59,0.6)'}}>90.0$ par mois</Text>
                <Text style={{color:'rgba(200, 41, 59,0.6)'}}>78% sur 3300$</Text>
              </View>
              <View style={{alignItems:'center',flexDirection:'row',width:330,height:10,borderRadius:10,backgroundColor:'rgba(30, 41, 59,0.3)'}}>
                <View style={{width:250,height:7,borderRadius:10,backgroundColor:'#166534'}}></View>
              </View>
              {/* <Text style={styles.comptesNom}>Gilgen Mate Landry</Text> */}
            </View>
            <View style={{marginHorizontal:10,flexDirection:'row',gap:10}}>
              <FontAwesome5 name="calendar" size={14} color={Colors.dark.slate700}/>
              <Text>Prochain payement</Text>
              <Text style={{color:'rgba(200, 41, 59,0.6)'}}>26/07/2025</Text>
            </View>
          </View>
        </View>
        {/* demander pret action */}
        <View style={styles.credit}>
          <TouchableOpacity>
            <View style={{alignItems:'center',gap:10}}>
              <View style={styles.creditIconCont}>
                <FontAwesome5 name="hand-holding-usd" size={24} color="#fefefe"/>
              </View>
              <Text style={styles.text1} >Demander un prêt</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity>
            <View style={{alignItems:'center',gap:10}}>
              <View style={styles.creditIconCont}>
                <FontAwesome5 name="handshake" size={24} color="#fefefe"/>
              </View>
              <Text style={styles.text1} >Rembourser un prêt</Text>
            </View>
          </TouchableOpacity>
          
        </View>
        {/* rembourssement component */}
        <Text style={{color:'#fefefe',fontSize:24,marginVertical:20}}>Cycles de rembourssement</Text>
        <View style={styles.comptes}>
          <View style={styles.comptesHeader}>
            <View style={styles.transCards}>
              {remboursements.map((remboursement,index)=>(
                <TouchableOpacity onPress={()=>{setFocusRembourssement(focusRemboussement===index+1?0:index+1)}} key={index}>
                  <View style={styles.rembourssementCardGroup}>
                    <View style={styles.transCard}>
                      <View style={styles.persoCard}>
                        <FontAwesome5 name="check" style={styles.check} />
                        <View>
                          <Text  style={styles.text1}  >Rembourssement</Text>
                          <Text style={styles.text2} >{remboursement.Date_Prevu_Remb}</Text>
                        </View>
                      </View>
                      <Text style={styles.text3} >{remboursement.Montant_Capital.toFixed(1)}$</Text>
                      <FontAwesome5 name={focusRemboussement===index+1 ? 'chevron-up' : 'chevron-down'} size={16} color={Colors.light.background} />
                    </View>
                    {/* rembourssement detail */}
                    {focusRemboussement===index+1 && (
                      <View style={styles.rembourssemenDetailC}>
                      
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Date prévue de remboursement</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.Date_Prevu_Remb}</Text>
                        </View>
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Capital</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.Montant_Capital.toFixed(1)}</Text>
                        </View>
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Intéret</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.Montant_Interet.toFixed(1)}</Text>
                        </View>
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Pénalité</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.Montant_Penalite_Rembourser.toFixed(1)}</Text>
                        </View>
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Montant total à rembourser</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.Montant_Total_Rembourser.toFixed(1)}</Text>
                        </View>
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Remboursé</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.Remboursement}</Text>
                        </View>
                        <View style={styles.rembourssementItem}>
                          <Text style={styles.rembourssemenDetailH}>Nombre de jours de retard</Text>
                          <Text style={styles.rembourssemenDetail}>{remboursement.NombreJour_Retard}</Text>
                        </View>
                      </View>
                    )}
                    
                  </View>
                </TouchableOpacity>
              ))}
              
              
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
    </View>
  );
}
// rembourssemnt
const remboursements = [
  {
    Date_Prevu_Remb: "2024-11-25",
    Montant_Capital: 99.0,
    Montant_Interet: 60.0,
    Montant_Penalite_Rembourser: 0.0,
    Montant_Total_Rembourser: 159.0,
    Remboursement: "oui",
    NombreJour_Retard: 0,
  },
  {
    Date_Prevu_Remb: "2024-12-23",
    Montant_Capital: 101.0,
    Montant_Interet: 58.0,
    Montant_Penalite_Rembourser: 0.0,
    Montant_Total_Rembourser: 159.0,
    Remboursement: "oui",
    NombreJour_Retard: 0,
  },
  {
    Date_Prevu_Remb: "2025-01-25",
    Montant_Capital: 103.0,
    Montant_Interet: 56.0,
    Montant_Penalite_Rembourser: 0.0,
    Montant_Total_Rembourser: 159.0,
    Remboursement: "oui",
    NombreJour_Retard: 0,
  },
  {
    Date_Prevu_Remb: "2025-02-25",
    Montant_Capital: 105.0,
    Montant_Interet: 54.0,
    Montant_Penalite_Rembourser: 0.0,
    Montant_Total_Rembourser: 159.0,
    Remboursement: "oui",
    NombreJour_Retard: 0,
  },
  {
    Date_Prevu_Remb: "2025-03-25",
    Montant_Capital: 107.0,
    Montant_Interet: 52.0,
    Montant_Penalite_Rembourser: 0.0,
    Montant_Total_Rembourser: 159.0,
    Remboursement: "oui",
    NombreJour_Retard: 0,
  },
  {
    Date_Prevu_Remb: "2025-04-25",
    Montant_Capital: 109.0,
    Montant_Interet: 50.0,
    Montant_Penalite_Rembourser: 0.0,
    Montant_Total_Rembourser: 159.0,
    Remboursement: "oui",
    NombreJour_Retard: 0,
  }
];
const trucanteCode=(data:string):string=>{
  const subCode=data.slice(data.length-4,data.length)
  return `*****${subCode}`
}

interface MenuItemProps {
  icon: any;
  label: string;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, label }) => (
  <TouchableOpacity style={styles.menuItem}>
    <MaterialIcons name={icon} size={24} color={Colors.blue.icon} />
    <Text style={styles.menuItemLabel}>{label}</Text>
  </TouchableOpacity>
);

interface ActionCardProps {
  icon: any;
  label: string;
}

const ActionCard: React.FC<ActionCardProps> = ({ icon, label }) => (
  <TouchableOpacity style={styles.actionCard}>
    <FontAwesome5 name={icon} size={24} color="white" />
    <Text style={styles.actionCardLabel}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  containerFluid: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
  },
  creditIconCont:{
    backgroundColor:Colors.blue.blueTwo,
    width:60,
    height:60,
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'center',
    borderRadius:50,
  },
  credit: {
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    marginTop:24,
  },
  cardChoise:{
    backgroundColor:'rgba(30, 58, 138,0.7)',
    width:80,
    gap:2,
    borderRadius:10,
    overflow:'hidden',
    marginTop:3,
    // position:'absolute',
    // top:55,
  },
  compteItems:{
    paddingVertical:3,
    paddingHorizontal:10,
    backgroundColor:'rgba(30, 58, 138,0.1)',
    color:'white',
  },
  transCards:{
    gap: 10,
    paddingVertical: 16,
    flex:1,
  },
   rembourssementCardGroup:{
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    padding: 1,
    borderRadius: 16,
  },
  rembourssementItem:{
    flexDirection:'row',
    borderBottomColor:Colors.dark.slate700,
    borderBottomWidth:1,
    padding:5,
    justifyContent:'space-between',
  },
  rembourssemenDetailC:{
    padding:12,
    gap:8,
  },
  rembourssemenDetailH:{
    color:'white',
    fontWeight:'bold',
  },
  rembourssemenDetail:{
    color:'white',
    backgroundColor:Colors.dark.slate700,
    paddingVertical:3,
    paddingHorizontal:10,
    borderRadius:5,
  },
  transCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    padding: 12,
    borderRadius: 16,
  },
  persoCard: {
    flexDirection: 'row', 
    alignItems: 'center',
    gap: 20,
  },
  check:{
    color: '#4ade80',
    fontSize: 20,
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: 14,
  },
  persone:{
    color: 'white',
    fontSize: 20,
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: 14,
  },
  text1:{
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  text2:{
    color: 'white',
    fontSize: 12,
    fontWeight: '400'
  },
  text3:{
    color: 'white',
    fontSize: 15,
    fontWeight: '600'
  },
   lineTrough:{
    width:250,
    backgroundColor:'rgba(30, 58, 138,0.1)',
    // boxShadow:'1px 1px 50px rgba(0,0,0,0.3)',
    height:320,
    // flex:1,
    position:'absolute',
    top:-100,
    left:160,
    transform:'rotate(-35deg)',
    zIndex:-111
    // bottom:0,

  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    marginTop: 54,
    paddingTop: 16,
    marginBottom:14,
  },
   header: {
    backgroundColor: Colors.dark.slate800,
    position: 'sticky',
    top: 54,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    backgroundColor: Colors.blue.text,
    borderRadius: 999,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
  },
  salutation: {
    color: 'white',
    fontSize: 14,
    marginBottom: 16,
  },
  salutationName: {
    color: Colors.blue.text,
    fontWeight: 'bold',
  },
  menu: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 24, 
    gap: 5,
    maxWidth: screenWidth,
  },
  menuItem: {
    alignItems: 'center',
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: screenWidth * 0.016,
    paddingVertical: 16,   
  },
  menuItemLabel: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8, 
    maxWidth: screenWidth * 0.26,
  },
  solde: {
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  soldeText: {
    color: 'white',
    fontSize: 14,
  },
  soldeAction: {
    color: '#60a5fa',
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  actionCard: {
    backgroundColor: '#f28b82',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '45%',
  },
  actionCardLabel: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 8,
  },
  comptes: {
    marginBottom: 40,
    width:'auto',
  },
  comptesHeader: {
    width:'auto',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  comptesTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  comptesVoirTout: {
    color: 'white',
    fontSize: 12,
  },
  comptesvoir: {
    flexDirection: 'row',
    alignItems: 'center',
    gap:5,
  },
  
  comptesCard: {
    overflow:'hidden',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    height:200,
    justifyContent:'space-between',
    position:'relative',
    zIndex:333,
    boxShadow:'3px -5px 10px rgba(0,0,0,0.1)'
  },
  comptesNom: {
    color: '#7B4019',
    fontWeight: 'bold',
    fontSize: 16,
  },
  comptesDetailTwo:{
    color: '#fefefe',
    fontSize: 24,
    marginTop: 4,
  },
  comptesDetail: {
    color: Colors.dark.slate900,
    fontSize: 24,
    marginTop: 4,
  },
  soldeActionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  notifIcon: {
    backgroundColor: Colors.dark.slate700,
    padding: 8,
    borderRadius: 32,
  },
  ownerProp:{
    color:Colors.dark.slate700,
  }
});
