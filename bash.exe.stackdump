Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF77DC0000 ntdll.dll
7FFF76CE0000 KERNEL32.DLL
7FFF75370000 KERNELBASE.dll
7FFF76F70000 USER32.dll
7FFF75340000 win32u.dll
7FFF77BC0000 GDI32.dll
7FFF75980000 gdi32full.dll
7FFF75700000 msvcp_win.dll
7FFF75860000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF776B0000 advapi32.dll
7FFF75C80000 msvcrt.dll
7FFF77AE0000 sechost.dll
7FFF778D0000 RPCRT4.dll
7FFF74980000 CRYPTBASE.DLL
7FFF752C0000 bcryptPrimitives.dll
7FFF77B80000 IMM32.DLL
