import { Colors } from '@/constants/Colors';
import { FontAwesome5 } from '@expo/vector-icons';
import { Link } from 'expo-router';
import React, { useRef, useState } from 'react';
import { Alert, Dimensions, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
const screenWidth = Dimensions.get('window').width;
export default function Transaction() {
  const [showText, setShowText] = useState(false);
  const [numberAccount,setNumberAccount]=useState('')
  const [montant,setMontant]=useState('')
  const [isDisable,setIsDisable]=useState(true)
  const [showCompte,setShowCompte]=useState(false)
  const [currentCompte,setCurrentCompte]=useState('001-108397-00-31 / Pop Monnaie à Vue Staff USD')
  const [showOtp,setShowOtp]=useState(false)
    // fonction pour vaider le transfert
  const valideTranfert=()=>{
    console.log('valider transfert')
    if(montant ===''){
        Alert.alert('Montant est obligatoire')
    }else{
        console.log(montant)
        setShowOtp(true)
    }
  }   
  let [showUser,setShowUser]=useState(false)
  let [codenumber,setCodenumber]=useState([])
  let [codeOne,setCodeOne]=useState('')
  let [codeTwo,setCodeTwo]=useState('')
  let [codeThree,setCodeThree]=useState('')
  let [codeFour,setCodeFour]=useState('')
  let [codeFive,setCodeFive]=useState('')
  let [codeSix,setCodeSix]=useState('')
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const inputs = useRef<Array<TextInput | null>>([]);
//   poupop logique
  const handleChange = (text:string, index:number) => {
    if (/^\d$/.test(text)) {
      const newCode = [...code];
      newCode[index] = text;
      setCode(newCode);

      // focus au champ suivant
      if (index < 5) {
        inputs.current[index + 1]?.focus();
      }
    } 
  };
  const confirmationBtn=()=>{
    // console.log(code)
    Alert.alert('Transaction effectuer avec success')
    setTimeout(()=>{
        setShowOtp(false)
    },3000)
  }
//   switch compte
  const [switchCompte,setSwitchCompte]=useState(false)
  const [switchClient,setSwitchClient]=useState('066200039657052')

  return (
    <View style={styles.containerFluid}>
       {/* Header */}
      <View style={styles.header}>
        <View>
          <Link href="/">
            <FontAwesome5 name="chevron-left" size={20} color={Colors.blue.text} />
          </Link>
        </View>
        <Text style={styles.headerTitle}>Transaction</Text>
        <View style={styles.avatar}>
            <Text style={styles.avatarText}>JK</Text>
        </View>
      </View>
      <ScrollView style={styles.container}>
      <View style={{marginBottom:64}}>
        <View style={[styles.TransactionBackHeader,{position:'relative'}]}>
            <View>
                <TouchableOpacity onPress={()=>setSwitchCompte(!switchCompte)}>
                    <View style={[styles.transCard,{width:230,backgroundColor:'white'}]}>
                        <FontAwesome5 name="credit-card" size={14} color={Colors.blue.background} />
                        <Text style={[styles.text1,{color:Colors.blue.blueTwo}]}> {switchClient}</Text>
                        <FontAwesome5 name={switchCompte ?'chevron-up':'chevron-down'} size={14} color={Colors.blue.background} />
                    </View>
                </TouchableOpacity>
            </View>
            {switchCompte && (
                <View style={[styles.inputStyle,{backgroundColor:Colors.dark.slate900,height:100,justifyContent:'space-between',width:230,padding:10,gap:5,alignItems:'center',position:'absolute',top:65,zIndex:5555}]}>
                    <TouchableOpacity onPress={()=>{
                        setSwitchClient('066200039657052')
                        setSwitchCompte(false)
                        }}>
                        <View style={{alignItems:'center',flexDirection:'row',gap:5}}>
                            <View style={{backgroundColor:'white',width:30,height:30,borderRadius:50,alignItems:'center',justifyContent:'center'}}>
                                <Text style={[{color:Colors.dark.slate800},switchClient==='066200039657052'&& {color:Colors.blue.background}]}>1</Text>
                            </View>
                            <Text style={[styles.text2,{fontWeight:'bold'}]}>066200039657052</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        setSwitchClient('066200039658888')
                        setSwitchCompte(false)
                        }}>
                            <View style={{alignItems:'center',flexDirection:'row',gap:5}}>
                                <View style={{backgroundColor:'white',width:30,height:30,borderRadius:50,alignItems:'center',justifyContent:'center'}}>
                                    <Text style={[{color:Colors.dark.slate800},switchClient==='066200039658888'&& {color:Colors.blue.background}]}>2</Text>
                                </View>
                                <Text style={[styles.text2,{fontWeight:'bold'}]}>066200039658888</Text>
                            </View>
                    </TouchableOpacity> 
                </View>
            )}
            <View style={{gap: 20, padding: 16}}> 
                <View style={{flexDirection:'row', gap:16, alignItems: 'center'}}>
                    <Text style={{color: Colors.dark.icon, fontSize:16, fontWeight: 'bold'}}>Solde disponible</Text> 
                    <TouchableOpacity onPress={() => setShowText(!showText)} style={{flexDirection:'row', gap:5, alignItems: 'center'}}>
                    <FontAwesome5 name={showText ? 'chevron-up' : 'chevron-down'} size={12} color={Colors.blue.text} /> 
                    <Text style={{color: Colors.dark.icon, fontSize: 12}}>
                        {showText ? 'USD' : 'CDF'}
                    </Text>
                    </TouchableOpacity>
                </View>
                <Text style={{color: 'white', fontSize: 24, fontWeight: 'bold'}}>
                    {showText ? '1200 USD' : '560 000 CDF'}
                </Text>
                </View>
        </View>
        {/* formulaire d'envoie */}
        <View style={{marginVertical:20}}>
            <Text style={styles.text1}>Information destinateur</Text>
            <TextInput
                style={styles.inputStyle}
                onChangeText={(text)=>{
                    if(text===''){
                        setShowUser(false)
                        setIsDisable(true)
                    }
                    setIsDisable(false)
                    setNumberAccount(text)
                }}
                value={numberAccount}
                placeholder='Entrer le numero client'
                keyboardType='numeric'
            ></TextInput>
            <TouchableOpacity onPress={()=>{setShowUser(true)}} disabled={isDisable}>
                <View style={[styles.buttonPrimary,isDisable && {backgroundColor:'#60a5fa44'}]}>
                    <Text style={{color:'white'}}>Charger</Text>
                </View>
            </TouchableOpacity>
            {showUser===true && (
                // user charged 
                <View>
                    <View style={[styles.transCard,{marginTop:20,padding:30,justifyContent:'flex-start',gap:30,boxShadow:'3px 2px 1px rgba(0,0,0,0.5)'}]}>
                        <FontAwesome5 name="user" size={40} color='white'/>
                        <View>
                            <Text style={styles.text1}>BENESHA MUPINGANYI</Text>
                            <Text style={styles.text2}>************</Text>
                        </View>
                    </View>
                    <View style={{position:'relative'}}>
                        {/* show compte button */}
                        <TouchableOpacity onPress={()=>{setShowCompte(!showCompte)}}>
                            <View style={[styles.inputStyle,{height:50,flexDirection:'row',justifyContent:'space-between',paddingHorizontal:10,alignItems:'center'}]}>
                                <Text style={styles.text2}>{currentCompte}</Text>
                                <FontAwesome5 name={showCompte?'chevron-up':'chevron-down'} size={14} color={Colors.blue.background}/>
                            </View>
                        </TouchableOpacity>
                        {showCompte===true && (
                             <View style={[styles.inputStyle,{backgroundColor:Colors.dark.slate900,height:60,padding:10,gap:5,alignItems:'center',position:'absolute',top:53,zIndex:5555}]}>
                                <TouchableOpacity onPress={()=>{
                                    setCurrentCompte('001-108397-00-31 / Pop Monnaie à Vue Staff USD')
                                    setShowCompte(false)
                                    }}>
                                    <Text style={[styles.text2,{fontWeight:'bold'}]}>001-108397-00-31 / Pop Monnaie à Vue Staff USD</Text>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                    setCurrentCompte('001-108397-00-31 / Pop Monnaie à Vue Staff CDF')
                                    setShowCompte(false)
                                    }}>
                                        <Text style={[styles.text2,{fontWeight:'bold'}]}>001-108397-00-31 / Pop Monnaie à Vue Staff CDF</Text>
                                </TouchableOpacity> 
                            </View>
                        )}
                       
                        <TextInput
                            style={[styles.inputStyle,{marginVertical:0}]}
                            onChangeText={setMontant}
                            value={montant}
                            placeholder='Montant du transfert'
                            keyboardType='numeric'
                        ></TextInput>
                        <TouchableOpacity onPress={valideTranfert}>
                            <View style={[styles.buttonPrimary,{backgroundColor:Colors.dark.slate900,marginTop:10,padding:16}]}>
                                <Text style={{color:'white'}}>Valider le transfert</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            )}
            
        </View>
            
        </View>
    </ScrollView>

    {/* show poupop for confirmation transaction */}
    {showOtp && (
        <View style={styles.showOtp}>
            <Text style={styles.text1}>Confirmer le transfert</Text>
            <Text style={[styles.text2,{marginVertical:10}]}>Inserer Code Envoyer par SMS pour la confirmation de votre transfert d&lsquo;argent : </Text>
            <View style={{flexDirection:'row',gap:10}}>
                {code.map((digit,index)=>(
                    <TextInput
                    style={[styles.inputStyle,{width:40,borderRadius:5,borderColor:'#fefefe88',boxShadow:'2px 5px 5px rgba(0,0,0,0.4)'}]}
                        onChangeText={(text) => handleChange(text, index)}
                        value={digit}
                        keyboardType='numeric'
                        key={index}
                        ref={(ref) => {inputs.current[index] = ref}}
                    ></TextInput>
                ))}
                

            </View>
             <TouchableOpacity onPress={confirmationBtn}>
                <View style={[styles.buttonPrimary,{marginTop:10}]}>
                    <Text style={{color:'white',fontWeight:'bold'}}>Confirmer</Text>
                </View>
            </TouchableOpacity>
            <Text style={[styles.text2,{marginTop:10}]}>Le code envoyé expire dans 60 Sec</Text>
        </View>
    )}
    </View>
  );
}

const styles = StyleSheet.create({
  showOtp:{
    position:'absolute',
    backgroundColor:'#1e293bef',
    top:0,
    bottom:0,
    left:0,
    right:0,
    alignItems:'center',
    justifyContent:'center',
    padding:20
  },
  buttonPrimary:{
    backgroundColor:Colors.blue.background,
    padding:10,
    alignItems:'center',
    borderRadius:10
  } , 
  inputStyle:{
    height:50,
    borderStyle:'solid',
    borderWidth:2,
    borderRadius:20,
    paddingHorizontal:10,
    borderColor:Colors.dark.slate700,
    marginVertical:10,
    color:'white',
    backgroundColor:'rgba(30, 41, 59, 0.8)',
  } , 
  containerFluid: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
  },
  TransactionBackHeader:{
    backgroundColor:Colors.dark.slate700,
    height:200,
    alignItems:'center',
    justifyContent:'center',
  },
   rembourssementCardGroup:{
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    padding: 1,
    borderRadius: 16,
  },
  transCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    padding: 12,
    borderRadius: 16,
  },
  persone:{
    color: 'white',
    fontSize: 20,
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: 14,
  },
  text1:{
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  text2:{
    color: 'white',
    fontSize: 12,
    fontWeight: '400'
  },
  text3:{
    color: 'white',
    fontSize: 15,
    fontWeight: '600'
  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    marginTop: 54,
    paddingTop: 16,
    marginBottom:14,
  },
   header: {
    backgroundColor: Colors.dark.slate800,
    position: 'sticky',
    top: 54,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    backgroundColor: Colors.blue.text,
    borderRadius: 999,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
  },
  soldeText: {
    color: 'white',
    fontSize: 14,
  },
  soldeAction: {
    color: '#60a5fa',
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  actionCard: {
    backgroundColor: '#f28b82',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '45%',
  },
  actionCardLabel: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 8,
  },
  comptes: {
    marginBottom: 40,
    width:'auto',
  },
  comptesHeader: {
    width:'auto',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  
});
