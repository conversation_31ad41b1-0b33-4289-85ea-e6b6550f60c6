import { Colors } from "@/constants/Colors";
import FontAwesome5 from "@expo/vector-icons/build/FontAwesome5";
import { Image } from 'expo-image';
import { router } from "expo-router";
import { useState } from "react";
import { Dimensions, Linking, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

const screenWidth = Dimensions.get('window').width;
const height = Dimensions.get('window').height;

export default function PayMobileScreen() {
    
  let [pressed, setPressed] = useState(false);
  let [pressed2, setPressed2] = useState(false);
  let [pressed3, setPressed3] = useState(false);
  const [showDevise, setDevise] = useState(false);
  const [forms, setForms] = useState({
    numero: Number,
    montant: '',
  })
  const openURL = (url: any) => {
    Linking.openURL(url).catch(err => {
      console.warn("Erreur lors de l'ouverture du lien :", err);
    });
  };
return (
    <SafeAreaView style={{flex:1, backgroundColor: Colors.dark.slate800, paddingVertical: 30, paddingHorizontal: 16}}>
        <View style={{gap: 10, width: screenWidth }}>
            <View style={{flexDirection:'row',alignItems:'center',gap:16, width: screenWidth * 0.9}}>
                <TouchableOpacity onPress={() => {router.back();}}>
                    <FontAwesome5 name="chevron-left" size={20} color={'white'} /> 
                </TouchableOpacity>
                <Text style={{color: 'white', fontSize: 16, fontWeight: 'bold'}}>Retour</Text>
            </View>
             <Text style={{fontSize: 12, fontWeight: 'bold', color: Colors.dark.icon, width: screenWidth, paddingLeft: 24, paddingTop: 16}}>Choisir un mode de transaction</Text>
            <View style={{width: screenWidth * 0.8,marginTop: 10, gap: 6, flexDirection: 'row', alignItems: 'center',}}> 
                
                <TouchableOpacity style={{
                    backgroundColor: pressed? Colors.dark.icon : Colors.dark.slate600,
                    padding: 10,
                    borderRadius: 12,
                    borderColor: pressed? Colors.dark.icon : Colors.dark.icon,
                    borderWidth: 0,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center', 
                    gap: 8, 
                }} onPress={() => {
                    setPressed(!pressed)
                    setPressed3(pressed3 = false)
                    setPressed2(pressed2 = false)
                }}>
                    <Image  source={require('@/assets/images/mobilePay/mpsa.png')} style={{width: 24, height: 24, objectFit: 'contain', marginVertical: 16}}/>
                    <Text style={{color: 'white', fontWeight: 'bold'}}>Mpesa</Text>  
                </TouchableOpacity> 
                <TouchableOpacity style={{
                    backgroundColor: pressed2? Colors.dark.icon : Colors.dark.slate600,
                    padding: 10,
                    borderRadius: 12,
                    borderColor: pressed2? Colors.dark.icon : Colors.dark.icon,
                    borderWidth: 0,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center', 
                    gap: 8, 
                }} onPress={() => {
                    setPressed2(!pressed2)
                    setPressed(pressed = false)
                    setPressed3(pressed3= false)
                }}>
                    <Image  source={require('@/assets/images/mobilePay/orange.png')} style={{width: 24, height: 24, objectFit: 'contain', marginVertical: 16}}/>
                    <Text style={{color: 'white', fontWeight: 'bold'}}>Orange M</Text>  
                </TouchableOpacity> 
                <TouchableOpacity style={{
                    backgroundColor: pressed3? Colors.dark.icon : Colors.dark.slate600,
                    padding: 10,
                    borderRadius: 12,
                    borderColor: pressed3? Colors.dark.icon : Colors.dark.icon,
                    borderWidth: 0,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center', 
                    gap: 8, 
                }} onPress={() => {
                    setPressed3(!pressed3)
                    setPressed(pressed = false)
                    setPressed2(pressed2 = false)
                }}>
                    <Image  source={require('@/assets/images/mobilePay/airtel.png')} style={{width: 24, height: 24, objectFit: 'contain', marginVertical: 16}}/>
                    <Text style={{color: 'white', fontWeight: 'bold'}}>Airtel M</Text>  
                </TouchableOpacity> 
            </View>

            <View>
                <View style={{gap:10, position: 'relative', marginTop: 24}}>
                                    <Text style={{color: Colors.dark.icon}}>Numéro <Text style={{color: Colors.blue.icon}}>{pressed? 'MPESA': ''} {pressed2? 'ORANGE MONEY': ''} {pressed3? 'AIRTEL MONEY': ''}</Text></Text>
                                          <TextInput
                                            style={[styles.inputStyle, {borderColor: 'white', borderWidth: 1}]}
                                            placeholder="Entrez le numéro du béneficiaire"
                                            placeholderTextColor="#9ca3af"
                                            keyboardType='numeric'
                                            textContentType="telephoneNumber"
                                            autoCapitalize="none"
                                            
                                            autoCorrect={false} 
                                            onFocus={() => {
                                                //donner border à l'input
                
                                            }}
                                          /> 
                                </View>
                <View style={{gap:10, position: 'relative', marginTop: 24}}>
                                    <Text style={{color: Colors.dark.icon}}>Devise  <TouchableOpacity onPress={() => setDevise(!showDevise)} style={{flexDirection:'row', gap:5, alignItems: 'center'}}>
                <FontAwesome5 name={showDevise ? 'chevron-up' : 'chevron-down'} size={12} color={Colors.blue.text} /> 
                <Text style={{color: Colors.dark.icon, fontSize: 12}}>
                  {showDevise ? 'USD' : 'CDF'}
                </Text>
              </TouchableOpacity></Text>
                                          <TextInput
                                            style={[styles.inputStyle, {borderColor: 'white', borderWidth: 1}]}
                                            placeholder= {showDevise ? 'Montant en USD' : 'Montant en CDF'}
                                            placeholderTextColor="#9ca3af"
                                            keyboardType='numeric' 
                                            autoCapitalize="none"
                                            autoCorrect={false}   
                                            value={forms.montant}
                                            onChangeText={
                                                (text) => setForms({...forms, montant: text})
                                            }
                                            onFocus={() => {
                                                //donner border à l'input
                
                                            }}
                                          /> 
                                </View>
            </View>

            <View style={{backgroundColor: Colors.dark.slate700, padding: 16, width: screenWidth*0.9, marginTop: 24, gap: 16}}>
                <View style={{flexDirection: 'row', gap: 8}}>
                    <Text style={{color: 'white', fontFamily: 'bold', fontSize: 14}}>Montant Total: </Text> <Text style={{color: Colors.dark.slate300, fontSize: 14}}> {forms.montant} {showDevise ? 'USD' : 'CDF'} + frais de transaction</Text>
                </View>
              <Text style={{ color: Colors.dark.slate300, fontSize: 14, width: screenWidth * 0.8 }}>
      En procédant au paiement, vous acceptez nos{' '}
      <Text
        style={{ color: Colors.blue.icon, textDecorationLine: 'underline' }}
        onPress={() => openURL('https://site.com/conditions')}
      >
        Conditions d&lsquo;utilisation
      </Text>{' '}
      et confirmez que vous avez lu notre{' '}
      <Text
        style={{ color: Colors.blue.icon, textDecorationLine: 'underline' }}
        onPress={() => openURL('https://site.com/politique')}
      >
        Politique de confidentialité
      </Text>
      . Vous pouvez annuler la transaction à tout moment.
    </Text>
            </View>
            
                <TouchableOpacity style={{
                    backgroundColor: forms.montant && forms.numero> 0 ? Colors.blue.background : Colors.dark.slate700,
                    padding: 12,
                    borderRadius: 24,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10,
                    width: screenWidth*0.9,
                }} onPress={() => {
                    
                }}>
                    <Text style={{color: 'white', fontWeight: 'bold'}}>Envoyer</Text>  
                </TouchableOpacity>
            
        </View>
    </SafeAreaView>
)
}

const styles = StyleSheet.create({
    inputStyle:{
        height:46, 
        width: screenWidth* 0.9,
        borderRadius:20,
        paddingHorizontal:20,
        backgroundColor:Colors.dark.slate700, 
        color:'white', 

    }
})