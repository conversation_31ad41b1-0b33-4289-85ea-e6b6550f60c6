import { Colors } from '@/constants/Colors';
import { FontAwesome5, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';
import React from 'react';
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
const screenWidth = Dimensions.get('window').width;

  const redirectToCompte = (url: any) => {
  router.push(url); // ✅
};
export default function HomeScreen() {
  return (
    <View style={styles.containerFluid}>
      <View style={styles.bgDegrader}></View>
      {/* Header */}
      <View  style={styles.header}>
        <TouchableOpacity onPress={() => redirectToCompte('/compte')}  style={styles.avatar}>
          <Text style={styles.avatarText}>JK</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Accueil</Text>
        <TouchableOpacity onPress={()=>router.push('/(tabs)/notification')}>
          <Ionicons name="notifications-outline" size={24} color="white" style={styles.notifIcon} />
        </TouchableOpacity>
      </View>
    <ScrollView style={styles.container}>

      {/* Salutation */}
      <Text style={styles.salutation}>
        Salut!, <Text style={styles.salutationName}>Jéremie</Text>
      </Text>

      {/* Menu */}
      <View style={styles.menu}>
        <MenuItem icon="send" label="Transfert d'Argent" url="/transaction" />
        <MenuItem icon="account-balance" label="Suivi de rembousement" url="/credit" />
        <MenuItem icon="receipt" label="Historique des transactions" url="/historique" />
      </View>

      {/* Solde */}
      <View style={styles.solde}>
        <Text style={styles.soldeText}>Votre solde</Text>
        <TouchableOpacity style={styles.soldeActionContainer} onPress={() => redirectToCompte('/compte')}>
          <Text style={styles.soldeAction}>Voir le solde</Text>
          <FontAwesome5 name="eye" size={16} color={Colors.blue.text} />
        </TouchableOpacity>
      </View>

      {/* Actions */}
      {/* <View style={styles.actions}>
        <ActionCard icon="calculator" label="Effectuer" />
        <ActionCard icon="coffee" label="Emprunter" />
      </View> */}

      {/* Comptes */}
      <View style={styles.comptes}>
        <View style={styles.comptesHeader}>
          <Text style={styles.comptesTitle}>Mes comptes <Text>(2)</Text></Text>
          <TouchableOpacity onPress={() => redirectToCompte('/compte')} style={styles.comptesvoir}> 
          <Text style={styles.comptesVoirTout}>Voir tout </Text>
          <FontAwesome5 name="chevron-right" size={12} color={Colors.blue.text} />
          </TouchableOpacity>
        </View>
        {/* comptes section */}
        <View style={{padding:5}}>
            {/* <LinearGradient
              colors={['#93c5fd', '#eeeeff']} // Bleu → Blanc
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.comptesCardTwo}
            >
              <Text style={styles.comptesDetail}>{trucanteCode('066200039658877')}</Text> 
              <View>

              </View>
            </LinearGradient> */}
            <View style={styles.comptesCardTwo}>
              <Text style={styles.comptesDetailTwo}>{trucanteCode('066200039658877')}</Text>
              <View style={{position:'relative'}}>
                <View style={{width:30, height:30,borderRadius:50,borderWidth: 2,borderColor: '#fefefe'}}></View>  
                <View style={{width:30, height:30,borderRadius:50,borderWidth: 2,borderColor: '#fefefe',backgroundColor:'#fefefe',position:'absolute',top:0,left:-15}}></View>   
              </View> 
            </View>
            <View style={styles.comptesCard}>
              <View style={styles.lineTrough}></View>
              <View style={{justifyContent:'space-between',flexDirection:'row',alignItems:'center'}}>
                <Text style={styles.comptesDetail}>{trucanteCode('066200039657052')}</Text> 
                <Text style={{color:Colors.dark.slate700}}>Compte courant</Text>
              </View>
              <View style={{justifyContent:'space-between',flexDirection:'row', alignItems:'flex-end'}}>
                <View>
                {/* <Text style={styles.comptesDetail}>066200039657052 • Compte courant</Text> */}
                  <Text style={styles.ownerProp}>Proprieteur de la carte</Text>
                  <Text style={styles.comptesNom}>Gilgen Mate Landry</Text>
                </View>
                <Text>12/25</Text>
              </View>
            </View>
        </View>
        
      </View>
      {/* transations */}
      <View style={styles.comptes}>
        <View style={styles.comptesHeader}>
          <View>
            <Text style={styles.soldeText}>Récents</Text>
            <Text style={styles.comptesTitle}>Mes transactions</Text>
          </View>
          <TouchableOpacity onPress={() => redirectToCompte('/transaction')} style={styles.comptesvoir}> 
          <FontAwesome5 name="chevron-right" size={24} color={Colors.blue.text} />
          </TouchableOpacity>
        </View>
        <View style={styles.transCards}>
          <View style={styles.transCard}>
            <View style={styles.persoCard}>
              <FontAwesome5 name="user" style={styles.persone} />
              <View>
                <Text  style={styles.text1}  >Jack Patrick</Text>
                <Text style={styles.text2} >Aujourd&apos;hui, 16h 26</Text>
              </View>
            </View>
            <Text style={styles.text3} >$26</Text>
          </View>
          <View style={styles.transCard}>
            <View style={styles.persoCard}>
              <FontAwesome5 name="user" style={styles.persone} />
              <View>
                <Text  style={styles.text1}  >Jack Patrick</Text>
                <Text style={styles.text2} >Aujourd&apos;hui, 16h 26</Text>
              </View>
            </View>
            <Text style={styles.text3} >$26</Text>
          </View>
          <View style={styles.transCard}>
            <View style={styles.persoCard}>
              <FontAwesome5 name="user" style={styles.persone} />
              <View>
                <Text  style={styles.text1}  >Jack Patrick</Text>
                <Text style={styles.text2} >Aujourd&apos;hui, 16h 26</Text>
              </View>
            </View>
            <Text style={styles.text3} >$26</Text>
          </View>
        </View>
      </View>
    </ScrollView>
    </View>
  );
}

const trucanteCode=(data:string):string=>{
  const subCode=data.slice(data.length-4,data.length)
  return `*****${subCode}`
}
interface MenuItemProps {
  icon: any;
  label: string;
  url: any;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, label, url }) => (
  <TouchableOpacity style={styles.menuItem} onPress={() => redirectToCompte(url)}>
    <MaterialIcons name={icon} size={24} color={Colors.blue.icon} />
    <Text style={styles.menuItemLabel}>{label}</Text>
  </TouchableOpacity>
);

interface ActionCardProps {
  icon: any;
  label: string;
}

const ActionCard: React.FC<ActionCardProps> = ({ icon, label }) => (
  <TouchableOpacity style={styles.actionCard}>
    <FontAwesome5 name={icon} size={24} color="white" />
    <Text style={styles.actionCardLabel}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  containerFluid: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    position: 'relative',
  },
  bgDegrader: {
    position: 'absolute',
    top: -100,
    left: 80,
    width: screenWidth, 
    transform: [{ rotate: '340deg' }],
    height: 800,
    borderLeftColor: '#1e293b00',
    borderLeftWidth: 0,
    backgroundColor: '#1e293b00',

  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    marginTop: 54,
    paddingTop: 16,
  },
  header: {
    backgroundColor: Colors.dark.slate800,
    position: 'sticky',
    top: 54,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    backgroundColor: Colors.blue.text,
    borderRadius: 999,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
  },
  salutation: {
    color: 'white',
    fontSize: 14,
    marginBottom: 16,
  },
  salutationName: {
    color: Colors.blue.text,
    fontWeight: 'bold',
  },
  menu: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 24, 
    gap: 5,
    maxWidth: screenWidth,
  },
  menuItem: {
    alignItems: 'center',
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: screenWidth * 0.016,
    paddingVertical: 16,   
  },
  menuItemLabel: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8, 
    maxWidth: screenWidth * 0.26,
  },
  solde: {
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  soldeText: {
    color: 'white',
    fontSize: 14,
  },
  soldeAction: {
    color: '#60a5fa',
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  actionCard: {
    backgroundColor: '#f28b82',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '45%',
  },
  actionCardLabel: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 8,
  },
  comptes: {
    marginBottom: 40,
  },
  comptesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  comptesTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  comptesVoirTout: {
    color: 'white',
    fontSize: 12,
  },
  comptesvoir: {
    flexDirection: 'row',
    alignItems: 'center',
    gap:5,
  },
  lineTrough:{
    width:250,
    backgroundColor:'rgba(30, 58, 138,0.1)',
    // boxShadow:'1px 1px 50px rgba(0,0,0,0.3)',
    height:320,
    // flex:1,
    position:'absolute',
    top:-100,
    left:160,
    transform:'rotate(-35deg)',
    zIndex:-111
    // bottom:0,

  },
  comptesCardTwo:{
    backgroundColor:Colors.blue.blueTwo,
    padding: 16,
    paddingBottom:20,
    borderTopLeftRadius:10,
    borderTopRightRadius:10,
    zIndex:11,
    transform:'translateY(10px)',
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center',
  },
  comptesCard: {
    overflow:'hidden',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    height:200,
    justifyContent:'space-between',
    position:'relative',
    zIndex:333,
    boxShadow:'3px -5px 10px rgba(0,0,0,0.1)'
  },
  comptesNom: {
    color: '#7B4019',
    fontWeight: 'bold',
    fontSize: 16,
  },
  comptesDetailTwo:{
    color: '#fefefe',
    fontSize: 24,
    marginTop: 4,
  },
  comptesDetail: {
    color: Colors.dark.slate900,
    fontSize: 24,
    marginTop: 4,
  },
  soldeActionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  notifIcon: {
    backgroundColor: Colors.dark.slate700,
    padding: 8,
    borderRadius: 32,
  },
  transCards:{
    gap: 5,
    paddingVertical: 16
  },
  transCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  persoCard: {
    flexDirection: 'row', 
    alignItems: 'center',
    gap: 20,
  },
  persone:{
    color: Colors.dark.icon,
    fontSize: 16,
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: 18,
  },
  text1:{
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  text2:{
    color: Colors.dark.icon,
    fontSize: 12,
    fontWeight: '400'
  },
  text3:{
    color: Colors.dark.icon,
    fontSize: 15,
    fontWeight: '600'
  },
  ownerProp:{
    color:Colors.dark.slate700,
  }
});
