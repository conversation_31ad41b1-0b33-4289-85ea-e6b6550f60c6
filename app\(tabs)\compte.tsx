import { Colors } from '@/constants/Colors';
import { useState } from 'react';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import React from 'react'; 
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View, LayoutAnimation, Platform, UIManager, FlatList, Alert } from 'react-native'; 
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import { router } from 'expo-router';

const screenWidth = Dimensions.get('window').width; 
 
const redirectToCompte = (url: any) => {
  router.push(url); // ✅
};

const transactions = {
  completed: [
    { id: '1', label: 'Paiement Airtel', date: '14 juin', montant: '-5 000 FC' },
    { id: '2', label: 'Recharge carte', date: '13 juin', montant: '-10 000 FC' },
  ],
  inProgress: [],
};

// renderItem doit être déclaré avant pour être accessible partout
const renderItem = ({ item }: { item: any }) => (
  <View style={styles.transItem}>
    <Text style={styles.transLabel}>{item.label}</Text>
    <Text style={styles.transDate}>{item.date}</Text>
    <Text style={styles.transMontant}>{item.montant}</Text>
  </View>
);

const EffectuerRoute = () => (
  <View style={{ padding: 16 }}>
    <Text style={styles.title}>Effectuer une transaction</Text>
    <TouchableOpacity style={styles.actionButton} onPress={() => redirectToCompte('/transaction')}>
      <Ionicons name="cash-outline" size={20} color="white" />
      <Text style={styles.actionText}>Envoyer de l&lsquo;argent</Text>
    </TouchableOpacity>
    <TouchableOpacity style={styles.actionButton} onPress={() => {
    Alert.alert('Confirmation du Compte', 'Continuer avec ce compte ?', [
      {
        text: 'Non',
        onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {text: 'Oui', onPress: () => redirectToCompte('/payMobile')},
    ]);}}>
      <Ionicons name="phone-portrait-outline" size={20} color="white" />
      <Text style={styles.actionText}>Envoi via Mobile Money</Text>
    </TouchableOpacity>
    <TouchableOpacity style={styles.actionButton} onPress={() => redirectToCompte('/transaction')}>
      <Ionicons name="card-outline" size={20} color="white" />
      <Text style={styles.actionText}>Payer une facture</Text>
    </TouchableOpacity>
  </View>
);


const DetailCompteRoute = () => (
  <View style={{ padding: 16 }}>
    <Text style={styles.title}>Détail du compte</Text>
    <View style={styles.detailBox}>
      <Text style={styles.label}>Numéro :</Text>
      <Text style={styles.value}>0662 0003 9657 052</Text>
    </View>
    <View style={styles.detailBox}>
      <Text style={styles.label}>Propriétaire :</Text>
      <Text style={styles.value}>Gilgen Mate Landry</Text>
    </View>
    <View style={styles.detailBox}>
      <Text style={styles.label}>Type :</Text>
      <Text style={styles.value}>Compte courant</Text>
    </View>
    <View style={styles.detailBox}>
      <Text style={styles.label}>Date d&lsquo;ouverture :</Text>
      <Text style={styles.value}>15 février 2024</Text>
    </View>
    <View style={styles.detailBox}>
      <Text style={styles.label}>Solde actuel :</Text>
      <Text style={styles.value}>560 000 CDF</Text>
    </View> 
  
  </View>
);


const ReleveRoute = () => (
  <FlatList
    data={transactions.completed}
    keyExtractor={(item) => item.id}
    renderItem={renderItem}
    ListHeaderComponent={() => (
      <Text style={styles.title}>Relevé des transactions</Text>
    )}
    ListEmptyComponent={
      <Text style={{ color: '#9ca3af', textAlign: 'center', marginTop: 20 }}>
        Aucun mouvement trouvé
      </Text>
    }
    contentContainerStyle={{ padding: 16 }}
  />
);


const GererCarteRoute = () => (
  <View style={{ padding: 16 }}>
    <View style={styles.title2}>
      <Text style={styles.title}>Gérer votre carte 
    </Text>
      <TouchableOpacity style={{backgroundColor: Colors.dark.slate700, borderRadius: 32, paddingVertical: 8, paddingHorizontal: 10, marginTop: -10}}>
        <FontAwesome5 name="plus" size={12} color={'white'} />
      </TouchableOpacity>
    </View>
    <TouchableOpacity style={styles.actionButton}>
      <Ionicons name="refresh-outline" size={20} color="white" />
      <Text style={styles.actionText}>Renouveler la carte</Text>
    </TouchableOpacity>
    <TouchableOpacity style={styles.actionButton} >
      <Ionicons name="settings-outline" size={20} color="white" />
      <Text style={styles.actionText}>Changer le code PIN</Text>
    </TouchableOpacity>
    <TouchableOpacity style={styles.actionButton2}>
      <Ionicons name="lock-closed-outline" size={20} color="#991b1b" />
      <Text style={styles.actionText}>Bloquer la carte</Text>
    </TouchableOpacity>
  </View>
);



const renderScene = SceneMap({
  Effectuer: EffectuerRoute,
  DetailCompte: DetailCompteRoute,
  releve: ReleveRoute, 
  GererCarte: GererCarteRoute,
});

export default function TabTwoScreen() {
  const [showText, setShowText] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [carte, setCarte] = useState(1);
  const [expanded, setExpanded] = useState(true); 

  const [index, setIndex] = useState(0);
  const [routes] = useState([
    { key: 'Effectuer', title: 'Effectuer Transact.' },
    { key: 'DetailCompte', title: 'Details du Compte' },
    { key: 'releve', title: 'Voir le Relevé' }, 
    { key: 'GererCarte', title: 'Gérer les cartes' }, 
  ]);

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };
  const renderTabBar = (props: any) => (
  <TabBar
    {...props}
    indicatorStyle={{ backgroundColor: Colors.blue.tint, height: 2 }}   
    style={{ backgroundColor: Colors.dark.slate800,
    elevation: 0,  
    shadowOpacity: 0,   
    borderTopWidth: 0,  
    borderBottomWidth: 1,
    borderBottomColor: '#1e293b',
     }}  
    labelStyle={{ color: 'red', fontWeight: 'bold', fontSize: 14 }}  
    activeColor={Colors.blue.tint}   
    inactiveColor="#9ca3af"  

  />
);

  return (
  <View style={styles.containerFluid}>
    {/* Header */}
    <View style={styles.header}> 
      <TouchableOpacity onPress={() => {router.back();}}>
        <FontAwesome5 name="chevron-left" size={20} color={Colors.blue.text} /> 
      </TouchableOpacity>
      <Text style={styles.headerTitle}>
        Mes Comptes
        <View style={styles.roundeNbr}>
          <Text style={{color: Colors.blue.text, fontSize: 10, fontWeight: '700'}}>{carte}</Text>
        </View>
      </Text> 
      <TouchableOpacity onPress={() => setShowPopup(!showPopup)} style={styles.cards}>
        <Ionicons name="card" size={20} color={Colors.blue.text} style={styles.card1}/>
        <Ionicons name="card" size={20} color={"white"} style={styles.card2}/>
      </TouchableOpacity>
    </View> 

    {/* Le contenu au-dessus des tabs reste scrollable via FlatList header */}
    <FlatList
      ListHeaderComponent={
        <>
          {/* Comptes */}
          <View style={styles.comptes}> 
            <View style={{padding:5, position: 'relative'}}>
              <View style={styles.comptesCard}>
                <View style={styles.lineTrough}></View>
                <View style={{justifyContent:'space-between',flexDirection:'row',alignItems:'center'}}>
                  <Text style={styles.comptesDetail}>{trucanteCode('066200039657052')}</Text> 
                  <Text style={{color:'white'}}>Compte courant</Text>
                </View>
                <View style={{justifyContent:'space-between',flexDirection:'row', alignItems:'flex-end'}}>
                  <View>
                    <Text style={styles.ownerProp}>Proprietaire de la carte</Text>
                    <Text style={styles.comptesNom}>Gilgen Mate Landry</Text>
                  </View>
                  <Text style={{color:Colors.dark.icon}}>12/25</Text>
                </View>
              </View>
            </View>
          </View>

          {/* solde disponible */}
          <View style={{gap: 20, padding: 16}}> 
            <View style={{flexDirection:'row', gap:16, alignItems: 'center'}}>
              <Text style={{color: Colors.dark.icon, fontSize:16, fontWeight: 'bold'}}>Solde disponible</Text> 
              <TouchableOpacity onPress={() => setShowText(!showText)} style={{flexDirection:'row', gap:5, alignItems: 'center'}}>
                <FontAwesome5 name={showText ? 'chevron-up' : 'chevron-down'} size={12} color={Colors.blue.text} /> 
                <Text style={{color: Colors.dark.icon, fontSize: 12}}>
                  {showText ? 'USD' : 'CDF'}
                </Text>
              </TouchableOpacity>
            </View>
            <Text style={{color: 'white', fontSize: 24, fontWeight: 'bold'}}>
              {showText ? '1200 USD' : '560 000 CDF'}
            </Text>
          </View>

          {/* Onglets */}
          <TabView
            navigationState={{ index, routes }}
            renderScene={renderScene}
            onIndexChange={setIndex}
            initialLayout={{ width: screenWidth }}
            renderTabBar={renderTabBar}
            style={{
              marginTop: 20,
              height: 600,
              width: screenWidth,
              alignSelf: 'center',
            }}
          />
        </>
      }
      data={[]} // FlatList sans données, utilisé juste pour scrollView + Header
      renderItem={null}
      keyExtractor={() => 'scroll'}
    />

    {/* Popup */}
    {showPopup && (
      <View style={styles.popup}>
        <View style={styles.popupContent}>
          <Text style={styles.popupText}>Changer de carte</Text>
          <View style={{gap: 10}}>
            <TouchableOpacity style={styles.popupButton} onPress={() => {setShowPopup(false); setCarte(1);}}>
              <Text style={styles.popupButtonText}>Carte 1</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.popupButton} onPress={() => {setShowPopup(false); setCarte(2);}}>
              <Text style={styles.popupButtonText}>Carte 2</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity onPress={() => setShowPopup(false)} style={styles.popupButton2}>
            <Text style={styles.popupButtonText}>Fermer</Text>
          </TouchableOpacity>
        </View>
      </View>
    )}
  </View>
);

}

const trucanteCode = (data: string): string => {
  const subCode = data.slice(data.length - 4, data.length);
  return `*****${subCode}`;
};

const styles = StyleSheet.create({
   containerFluid: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    marginTop: 54,
    paddingTop: 16,
  },
  header: {
    backgroundColor: Colors.dark.slate800,
    position: 'sticky',
    top: 54,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    zIndex: 1099,
    height: 54,
  }, 
  headerTitle: {
    color: 'white',
    fontSize: 18,
  }, 
  cards: {
    position: 'relative', 
  },  
  card1: {
    marginRight: 10,
  },
  card2: {
    position: 'absolute',
    left: 10,
    top: -5,
  },
  comptes: {
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  lineTrough: {
    width: 250,
    backgroundColor: 'rgba(209, 213, 225, 0.1)',
    height: 320,
    position: 'absolute',
    top: -100,
    left: 160,
    transform: [{ rotate: '-35deg' }],
    zIndex: -111,
  },
  comptesCard: {
    backgroundColor: Colors.blue.blueTwo,
    overflow: 'hidden', 
    borderRadius: 12,
    padding: 16,
    height: 200,
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 333,
    marginTop: 60,
  },
  comptesNom: {
    color: '#7B4019',
    fontWeight: 'bold',
    fontSize: 16,
  },
  comptesDetail: {
    color: 'white',
    fontSize: 24,
    marginTop: 4,
  },
  ownerProp: {
    color: Colors.dark.icon,
  },
  roundeNbr: {
    backgroundColor: 'white',  
    borderRadius: 50,
    position: 'absolute',
    top: -2,
    left: screenWidth * 0.4,
    zIndex: 999,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center', 
  },
  transItem: {
    backgroundColor: '#1f2937',  
    padding: 16,
    borderRadius: 16,
    marginBottom: 8,
  },
  transLabel: {
    color: 'white',
    fontSize: 16,
  },
  transDate: {
    color: '#9ca3af', 
    fontSize: 12,
  },
  transMontant: {
    color: '#34d399',  
    fontSize: 12,
  },
  toggleExpand: {
    marginBottom: 8,
  },
  toggleText: {
    textAlign: 'center',
    color: '#9ca3af',
  },
  title: {
  fontSize: 18,
  color: 'white',
  fontWeight: 'bold',
  marginBottom: 12,
},

  title2: {flexDirection:'row', alignItems: 'center', color: 'white', fontSize: 18, fontWeight: 'bold', marginBottom: 12,gap:12,
  },
actionButton: {
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: Colors.dark.slate700,
  paddingVertical: 32,
  paddingHorizontal: 12,
  borderRadius: 10,
  marginBottom: 12,
  gap: 10,
},
actionButton2: {
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: Colors.dark.slate700,
  paddingVertical: 32,
  paddingHorizontal: 12,
  borderRadius: 10,
  marginBottom: 12,
  gap: 10,
},
actionText: {
  color: 'white',
  fontSize: 16,
},
detailBox: {
  marginBottom: 10,
  backgroundColor: Colors.dark.slate700,
  padding: 12,
  borderRadius: 10,
},
label: {
  color: '#9ca3af',
  fontSize: 14,
},
value: {
  color: 'white',
  fontSize: 16,
  fontWeight: '600',
},
popup: {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 2222
},
popupContent: {
  backgroundColor: Colors.dark.slate700,
  minHeight: 350,
  minWidth: 250,
  padding: 20,
  gap:8,
  borderRadius: 12,
  borderLeftWidth: 1,
  borderColor: Colors.dark.slate800,
},
popupText: {
  fontSize: 16,
  marginBottom: 20,
  color: 'white',
},
popupButton: {
  backgroundColor: '#1e3a8a22',
  paddingVertical: 15,
  paddingHorizontal: 20,
  borderBottomWidth: 1,
  borderBottomColor: Colors.dark.slate800,
  borderRadius: 5,
},
popupButton2: {
   position: 'absolute',
   bottom: 0, 
   right: 0, 
   paddingVertical: 15,
   paddingHorizontal: 20,
   borderRadius: 5,
},
popupButtonText: {
  color: 'white',
  fontSize: 16,
},

});
