import React, { useEffect, useRef } from 'react';
import {
  Animated,
  Dimensions,
  PanResponder,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { Colors } from '@/constants/Colors';
import FontAwesome5 from '@expo/vector-icons/build/FontAwesome5';
import Ionicons from '@expo/vector-icons/build/Ionicons';
import { router } from 'expo-router';
import { Image } from 'expo-image';  

interface SideMenuProps {
  visible: boolean;
  onClose: () => void;
}

const screenWidth = Dimensions.get('window').width;
const menuWidth = screenWidth * 0.75;

export default function SideMenu({ visible, onClose }: SideMenuProps) {
  const translateX = useRef(new Animated.Value(-menuWidth)).current;
  const insets = useSafeAreaInsets();
  const redirectTo = (url: any) => {
    closeMenu();
    router.push(url); // ✅
  };

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => gestureState.dx < -10,
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dx < 0) {
          translateX.setValue(Math.max(-menuWidth, gestureState.dx));
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dx < -50) {
          closeMenu();
        } else {
          openMenu();
        }
      },
    })
  ).current;

  const openMenu = () => {
    Animated.timing(translateX, {
      toValue: 0,
      duration: 250,
      useNativeDriver: true,
    }).start();
  };

  const closeMenu = () => {
    Animated.timing(translateX, {
      toValue: -menuWidth,
      duration: 250,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };

  useEffect(() => {
    if (visible) openMenu();
    else translateX.setValue(-menuWidth);
  }, [visible]);

  if (!visible) return null;

  return (
    <View style={styles.wrapper}>
      <TouchableOpacity style={styles.overlay} onPress={closeMenu} activeOpacity={1} />

      <Animated.View
        style={[
          styles.menu,
          {
            paddingTop: insets.top + 20,
            paddingBottom: insets.bottom + 20,
            transform: [{ translateX }],
          },
        ]}
        {...panResponder.panHandlers}
      >
        <View style={{flexDirection:'row',alignItems:'center', justifyContent: 'space-between',gap:10}}>
        <View style={{flexDirection:'row',alignItems:'center',gap:10,}}>
          <Image source={require('../assets/images/icon.png')} style={{  width: 30, height: 30, position: 'relative', top: -14 }} />
          <Text style={styles.title}>
            Menu</Text>
        </View>
        <Ionicons name="close" size={24} color="white" onPress={onClose} style={{padding:10, backgroundColor: 'rgba(0,0,0,0.1)', borderRadius: 32, marginTop:-30}} />
        </View>

        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/')}>
          <FontAwesome5 name="home" size={20} color="white" />
          <Text style={styles.itemText}>Accueil</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/compte')}>
          <FontAwesome5 name="wallet" size={20} color="white" />
          <Text style={styles.itemText}>Compte</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/credit')}>
          <FontAwesome5 name="credit-card" size={20} color="white" />
          <Text style={styles.itemText}>Crédit</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/epargne')}>
          <FontAwesome5 name="money-bill-alt" size={20} color="white" />
          <Text style={styles.itemText}>Epargne EAC</Text>
        </TouchableOpacity> 
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/transaction')}>
          <FontAwesome5 name="money-bill" size={20} color="white" />
          <Text style={styles.itemText}>Transactions</Text>
        </TouchableOpacity> 
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/historique')}>
          <FontAwesome5 name="history" size={20} color="white" />
          <Text style={styles.itemText}>Historiques</Text>
        </TouchableOpacity> 
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/notification')}>
          <FontAwesome5 name="bell" size={20} color="white" />
          <Text style={styles.itemText}>Notifications</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.item} onPress={() => redirectTo('/login')}>
          <FontAwesome5 name="sign-out-alt" size={20} color="white" />
          <Text style={styles.itemText}>Se déconnecter</Text>
        </TouchableOpacity>

        {/* ESPACE PUB */}
        <View style={{marginTop: 50, gap: 10}}>
          <Image source={require('../assets/images/pub1.png')} style={{width: 'auto', height: 180, borderRadius: 12, position: 'relative', top: -60}} /> 
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 999,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  menu: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: menuWidth,
    backgroundColor: Colors.dark.slate700,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
    marginBottom: 30,
  },
  item: { 
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,  
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  itemText: {
    fontSize: 16,
    color: 'white',
  },
});
