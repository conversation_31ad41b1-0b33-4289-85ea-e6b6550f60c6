import { Colors } from '@/constants/Colors';
import { useState } from 'react';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import React from 'react'; 
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View, LayoutAnimation, Platform, UIManager, FlatList , TextInput} from 'react-native'; 
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import { router } from 'expo-router';

const screenWidth = Dimensions.get('window').width;
 

const transactions = {
  completed: [
    { id: '1', label: 'Paiement Airtel', date: '14 juin', montant: '-5 000 FC' },
    { id: '2', label: 'Recharge carte', date: '13 juin', montant: '-10 000 FC' },
  ],
  inProgress: [],
};

// renderItem doit être déclaré avant pour être accessible partout
const renderItem = ({ item }: { item: any }) => (
  <View style={styles.transItem}>
    <Text style={styles.transLabel}>{item.label}</Text>
    <Text style={styles.transDate}>{item.date}</Text>
    <Text style={styles.transMontant}>{item.montant}</Text>
  </View>
);

const CompletedRoute = () => (
  <FlatList
    data={transactions.completed}
    renderItem={renderItem}
    keyExtractor={(item) => item.id}
    contentContainerStyle={{ paddingHorizontal: 16 }}
  />
);

const InProgressRoute = () => (
  <FlatList
    data={transactions.inProgress}
    renderItem={renderItem}
    keyExtractor={(item) => item.id}
    ListEmptyComponent={
      <Text style={{ color: '#9ca3af', textAlign: 'center', marginTop: 20 }}>
        Aucune transaction en cours
      </Text>
    }
    contentContainerStyle={{ paddingHorizontal: 16 }}
  />
);

const renderScene = SceneMap({
  completed: CompletedRoute,
  inProgress: InProgressRoute,
});

export default function TabTwoScreen() {
  const [showText, setShowText] = useState(false);
  const [expanded, setExpanded] = useState(true);

  const [index, setIndex] = useState(0);
  const [routes] = useState([
    { key: 'completed', title: 'Completed' },
    { key: 'inProgress', title: 'In Progress' },
  ]);

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };
  const renderTabBar = (props: any) => (
  <TabBar
    {...props}
    indicatorStyle={{ backgroundColor: Colors.blue.tint, height: 2 }}   
    style={{ backgroundColor: Colors.dark.slate800,
    elevation: 0,  
    shadowOpacity: 0,   
    borderTopWidth: 0,  
    borderBottomWidth: 1,
    borderBottomColor: '#1e293b',
     }}  
    labelStyle={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}  
    activeColor={Colors.blue.tint}   
    inactiveColor="#9ca3af"  
  />
);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}> 
        <TouchableOpacity onPress={() => {router.back();}}>
          <FontAwesome5 name="chevron-left" size={20} color={Colors.blue.text} /> 
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          Historiques des transactions 
        </Text> 
          <TouchableOpacity onPress={()=>router.push('/(tabs)/notification')}>
                  <Ionicons name="notifications-outline" size={24} color="white" style={styles.notifIcon} />
                </TouchableOpacity>
      </View>    

   

     {/* recherche dans l'historique */}
     <View style={{marginBottom:20, marginTop:20}}> 
        <View style={{flexDirection:'row',alignItems:'center',gap:10, position: 'relative'}}>
          <TextInput
            style={{flex:1,backgroundColor: '#1e293b88', borderWidth: 1, borderColor: '#1e293b', borderRadius: 12, padding: 12, color: 'white'}}
            placeholder="Rechercher"
            placeholderTextColor="#9ca3af"
          />
          <FontAwesome5 name="search" size={16} color={Colors.dark.icon} style={{position: 'absolute', right: 12, top: 12}} /> 
        </View>
      </View>

      {/* Onglets avec swipe */}
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        initialLayout={{ width: Dimensions.get('window').width }}
        renderTabBar={renderTabBar}
        style={{ marginTop: 20, height: 400, gap: 20, width: screenWidth, alignSelf: 'center' }}
      />

      {/* Bouton pour dérouler */}
      {/* <TouchableOpacity onPress={toggleExpand} style={styles.toggleExpand}>
        <Text style={styles.toggleText}>
          Afficher {expanded ? 'moins ▲' : 'plus ▼'}
        </Text>
      </TouchableOpacity> */}

      {/* Liste des transactions selon l'état expanded */}
     {/*  {expanded && (
        <FlatList
          data={index === 0 ? transactions.completed : transactions.inProgress}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={{ paddingHorizontal: 16 }}
        />
      )} */}

    </View>
  );
}

const trucanteCode = (data: string): string => {
  const subCode = data.slice(data.length - 4, data.length);
  return `*****${subCode}`;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    paddingTop: 54,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },  
  headerTitle: {
    color: 'white',
    fontSize: 18,
  }, 
  cards: {
    position: 'relative', 
  },  
  card1: {
    marginRight: 10,
  },
  card2: {
    position: 'absolute',
    left: 10,
    top: -5,
  },
  comptes: {
    marginBottom: 40,
  },
  lineTrough: {
    width: 250,
    backgroundColor: 'rgba(209, 213, 225, 0.1)',
    height: 320,
    position: 'absolute',
    top: -100,
    left: 160,
    transform: [{ rotate: '-35deg' }],
    zIndex: -111,
  },
  comptesCard: {
    backgroundColor: Colors.blue.blueTwo,
    overflow: 'hidden', 
    borderRadius: 12,
    padding: 16,
    height: 200,
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 333,
    marginTop: 60,
  },
  comptesNom: {
    color: '#7B4019',
    fontWeight: 'bold',
    fontSize: 16,
  },
  comptesDetail: {
    color: 'white',
    fontSize: 24,
    marginTop: 4,
  },
  ownerProp: {
    color: Colors.dark.icon,
  },
  roundeNbr: {
    backgroundColor: 'white',  
    borderRadius: 50,
    position: 'absolute',
    top: -2,
    left: screenWidth * 0.4,
    zIndex: 999,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center', 
  },
  transItem: {
    backgroundColor: '#1f2937',  
    padding: 16,
    borderRadius: 16,
    marginBottom: 8,
  },
  transLabel: {
    color: 'white',
    fontSize: 16,
  },
  transDate: {
    color: '#9ca3af', 
    fontSize: 12,
  },
  transMontant: {
    color: '#34d399',  
    fontSize: 12,
  },
  toggleExpand: {
    marginBottom: 8,
  },
  toggleText: {
    textAlign: 'center',
    color: '#9ca3af',
  },
  
  notifIcon: {
    backgroundColor: Colors.dark.slate700,
    padding: 8,
    borderRadius: 32,
  },
});
