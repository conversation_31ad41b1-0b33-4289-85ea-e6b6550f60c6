import { ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View, Dimensions } from "react-native";
import { Image } from 'expo-image';   
import { Colors } from "@/constants/Colors";
import { router } from "expo-router";

const screenWidth = Dimensions.get('window').width;
const height = Dimensions.get('window').height;

export default function LoginScreen() {
return (
    <ScrollView style={{flex:1, backgroundColor: Colors.dark.slate800,}}>
        <View style={{alignItems: 'center', justifyContent: 'center', gap: 10, width: screenWidth , height: height}}>
            <Text style={{color: Colors.dark.icon, fontSize: 12}}>Veillez remplir tous les champs correctement SVP!</Text>
            <Image source={require('@/assets/images/icon.png')} style={{width: 68, height: 60, objectFit: 'contain', marginVertical: 16}} /> 
            <Text style={{fontSize: 24, fontWeight: 'bold', color: 'white'}}>Authentification</Text>
            <View style={{width: screenWidth * 0.8, gap: 20}}>
                <View style={{gap:10, position: 'relative'}}>
                    <Text style={{color: Colors.dark.icon}}>Numéro client</Text>
                          <TextInput
                            style={styles.inputStyle}
                            placeholder="Entrez votre numéro client"
                            placeholderTextColor="#9ca3af"
                            keyboardType='numeric'
                            textContentType="telephoneNumber"
                            autoCapitalize="none"
                            autoCorrect={false}

                            onFocus={() => {
                                //donner border à l'input

                            }}
                          /> 
                </View>
                <View style={{gap:10, position: 'relative'}}>
                    <Text style={{color: Colors.dark.icon}}>Mot de passe</Text>
                          <TextInput
                            style={styles.inputStyle}
                            placeholder="Entrez votre mot de passe"
                            placeholderTextColor="#9ca3af"
                            secureTextEntry
                            textContentType="password"
                            autoCapitalize="none"
                            autoCorrect={false}
                            onFocus={() => {
                                //donner border à l'input

                            }}
                          /> 
                </View>
                
                <TouchableOpacity style={{
                    backgroundColor: Colors.blue.background,
                    padding: 12,
                    borderRadius: 24,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10,
                }} onPress={() => {
                    router.push('/(tabs)');
                }}>
                    <Text style={{color: 'white', fontWeight: 'bold'}}>Se connecter</Text>  
                </TouchableOpacity>
                <TouchableOpacity>
                    <Text style={{color: Colors.dark.icon, textDecorationLine: 'underline', marginTop: 10}}>Mot de passe oublié?</Text>
                </TouchableOpacity>
            </View>
            
        </View>
    </ScrollView>
)
}

const styles = StyleSheet.create({
    inputStyle:{
        height:46, 
        borderRadius:20,
        paddingHorizontal:20,
        backgroundColor:Colors.dark.slate700, 
        color:'white', 

    }
})