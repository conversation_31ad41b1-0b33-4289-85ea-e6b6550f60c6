import { Colors } from '@/constants/Colors';
import { FontAwesome5, MaterialIcons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const screenWidth = Dimensions.get('window').width;

export default function Notification() {
  let [selectCart,setSelectCart]=useState(false)
  let [compteValue,setCompteValue]=useState('107265')
  const closeSwitchCompte=(value:string)=>{
    setSelectCart(!selectCart)
    setCompteValue(value)
  }
  let [focusRemboussement,setFocusRembourssement]=useState(0)
  return (
    <View style={styles.containerFluid}>
       {/* Header */}
      <View style={styles.header}>
        <View>
          <Link href="/">
            <FontAwesome5 name="chevron-left" size={20} color={Colors.blue.text} />
          </Link>
        </View>
        <Text style={styles.headerTitle}>Notification</Text>
        <View style={styles.avatar}>
            <Text style={styles.avatarText}>JK</Text>
        </View>
      </View>
      <ScrollView style={styles.container}>
      <View style={{marginBottom:64}}>
        <Text style={{color:'#fefefe',fontSize:24,marginVertical:20}}>Notifications</Text>
        <View style={styles.notificationContainer}>
          <View style={styles.notificationDay}>
            <Text style={styles.text1}>Aujourd&lsquo;hui</Text>
          </View>
          <View style={{gap:1}}>
            {/* #bbf7d0 */}            
            <View style={styles.notificationItems}>
              <Text style={[styles.text2,{position:'absolute',right:10,top:10,zIndex:5}]}>
                  Il y a 5h30
                </Text>
              <View style={styles.notificationSubItems}>
                <FontAwesome5 name="info" size={20} color={Colors.blue.background}/>
                <View>
                  <Text  style={styles.text1}  >Modification d&lsquo;information du compte</Text>
                  <Text style={styles.text2} >2024-11-25</Text>
                </View>
              </View>
            </View>
            <View style={styles.notificationItems}>
              <Text style={[styles.text2,{position:'absolute',right:10,top:10,zIndex:5}]}>
                  Il y a 12h30
                </Text>
              <View style={styles.notificationSubItems}>
                <FontAwesome5 name="money-bill-wave" size={20} color="#bbf7d0"/>
                <View>
                  <Text  style={styles.text1}>Remboursement</Text>
                  <Text style={styles.text2} >2024-11-25</Text>
                </View>
              </View>
            </View>

            <View style={styles.notificationItems}>
              <Text style={[styles.text2,{position:'absolute',right:10,top:10,zIndex:5}]}>
                  Il y a 12h30
                </Text>
              <View style={styles.notificationSubItems}>
                <FontAwesome5 name="money-bill-wave" size={20} color="#bbf7d0"/>
                <View>
                  <Text  style={styles.text1}>Remboursement</Text>
                  <Text style={styles.text2} >2024-11-25</Text>
                </View>
              </View>
            </View>
            
          </View>
        </View>
        {/* 2 */}
        <View style={styles.notificationContainer}>
          <View style={styles.notificationDay}>
            <Text style={styles.text1}>Lundi</Text>
          </View>
          <View style={{gap:1}}>           
            <View style={styles.notificationItems}>
              <Text style={[styles.text2,{position:'absolute',right:10,top:10,zIndex:5}]}>
                  24/06/2025
                </Text>
              <View style={styles.notificationSubItems}>
                <FontAwesome5 name="info" size={20} color={Colors.blue.background}/>
                <View>
                  <Text  style={styles.text1}  >Modification d&lsquo;information du compte</Text>
                  <Text style={styles.text2} >2024-11-25</Text>
                </View>
              </View>
            </View>
            <View style={styles.notificationItems}>
              <Text style={[styles.text2,{position:'absolute',right:10,top:10,zIndex:5}]}>
                  24/06/2025
                </Text>
              <View style={styles.notificationSubItems}>
                <FontAwesome5 name="money-bill-wave" size={20} color="#bbf7d0"/>
                <View>
                  <Text  style={styles.text1}>Remboursement</Text>
                  <Text style={styles.text2} >2024-11-25</Text>
                </View>
              </View>
            </View>
            
          </View>
        </View>
        
      </View>
    </ScrollView>
    </View>
  );
}

interface MenuItemProps {
  icon: any;
  label: string;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, label }) => (
  <TouchableOpacity style={styles.menuItem}>
    <MaterialIcons name={icon} size={24} color={Colors.blue.icon} />
    <Text style={styles.menuItemLabel}>{label}</Text>
  </TouchableOpacity>
);

interface ActionCardProps {
  icon: any;
  label: string;
}

const ActionCard: React.FC<ActionCardProps> = ({ icon, label }) => (
  <TouchableOpacity style={styles.actionCard}>
    <FontAwesome5 name={icon} size={24} color="white" />
    <Text style={styles.actionCardLabel}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  containerFluid: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
  },
  creditIconCont:{
    backgroundColor:Colors.blue.blueTwo,
    width:60,
    height:60,
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'center',
    borderRadius:50,
  },
  credit: {
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    marginTop:24,
  },
  cardChoise:{
    backgroundColor:'rgba(30, 58, 138,0.7)',
    width:80,
    gap:2,
    borderRadius:10,
    overflow:'hidden',
    marginTop:3,
    // position:'absolute',
    // top:55,
  },
  compteItems:{
    paddingVertical:3,
    paddingHorizontal:10,
    backgroundColor:'rgba(30, 58, 138,0.1)',
    color:'white',
  },
  transCards:{
    gap: 10,
    paddingVertical: 16,
    flex:1,
  },
   rembourssementCardGroup:{
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    padding: 1,
    borderRadius: 16,
  },
  rembourssementItem:{
    flexDirection:'row',
    borderBottomColor:Colors.dark.slate700,
    borderBottomWidth:1,
    padding:5,
    justifyContent:'space-between',
  },
  rembourssemenDetailC:{
    padding:12,
    gap:8,
  },
  rembourssemenDetailH:{
    color:'white',
    fontWeight:'bold',
  },
  rembourssemenDetail:{
    color:'white',
    backgroundColor:Colors.dark.slate700,
    paddingVertical:3,
    paddingHorizontal:10,
    borderRadius:5,
  },
  transCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
    backgroundColor: Colors.dark.slate900,
    padding: 12,
    borderRadius: 16,
  },
  notificationContainer:{
    gap:0,
  },
  notificationItems:{
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    // alignItems: 'center',
    marginBottom: 4,
    gap:20,
    justifyContent:'space-between',
    position:'relative',
  },
  notificationSubItems:{
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap:20,
  },
  notificationDay:{
    paddingVertical:20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
    backgroundColor: 'rgba(96, 165, 250,0.7)',
    padding: 12,
    transform:'translateY(10px)'
  },
  persoCard: {
    flexDirection: 'row', 
    alignItems: 'center',
    gap: 20,
  },
  check:{
    color: '#4ade80',
    fontSize: 20,
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: 14,
  },
  persone:{
    color: 'white',
    fontSize: 20,
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: 14,
  },
  text1:{
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  text2:{
    color: 'white',
    fontSize: 12,
    fontWeight: '400'
  },
  text3:{
    color: 'white',
    fontSize: 15,
    fontWeight: '600'
  },
   lineTrough:{
    width:250,
    backgroundColor:'rgba(30, 58, 138,0.1)',
    // boxShadow:'1px 1px 50px rgba(0,0,0,0.3)',
    height:320,
    // flex:1,
    position:'absolute',
    top:-100,
    left:160,
    transform:'rotate(-35deg)',
    zIndex:-111
    // bottom:0,

  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.slate800,
    paddingHorizontal: 16,
    marginTop: 54,
    paddingTop: 16,
    marginBottom:14,
  },
   header: {
    backgroundColor: Colors.dark.slate800,
    position: 'sticky',
    top: 54,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    backgroundColor: Colors.blue.text,
    borderRadius: 999,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
  },
  salutation: {
    color: 'white',
    fontSize: 14,
    marginBottom: 16,
  },
  salutationName: {
    color: Colors.blue.text,
    fontWeight: 'bold',
  },
  menu: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 24, 
    gap: 5,
    maxWidth: screenWidth,
  },
  menuItem: {
    alignItems: 'center',
    backgroundColor: Colors.dark.slate700,
    borderRadius: 12,
    padding: screenWidth * 0.016,
    paddingVertical: 16,   
  },
  menuItemLabel: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8, 
    maxWidth: screenWidth * 0.26,
  },
  soldeText: {
    color: 'white',
    fontSize: 14,
  },
  soldeAction: {
    color: '#60a5fa',
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  actionCard: {
    backgroundColor: '#f28b82',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '45%',
  },
  actionCardLabel: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 8,
  },
  comptes: {
    marginBottom: 40,
    width:'auto',
  },
  comptesHeader: {
    width:'auto',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  comptesTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  comptesVoirTout: {
    color: 'white',
    fontSize: 12,
  },
  comptesvoir: {
    flexDirection: 'row',
    alignItems: 'center',
    gap:5,
  },
  
  comptesCard: {
    overflow:'hidden',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    height:200,
    justifyContent:'space-between',
    position:'relative',
    zIndex:333,
    boxShadow:'3px -5px 10px rgba(0,0,0,0.1)'
  },
  comptesNom: {
    color: '#7B4019',
    fontWeight: 'bold',
    fontSize: 16,
  },
  comptesDetailTwo:{
    color: '#fefefe',
    fontSize: 24,
    marginTop: 4,
  },
  comptesDetail: {
    color: Colors.dark.slate900,
    fontSize: 24,
    marginTop: 4,
  },
  soldeActionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  notifIcon: {
    backgroundColor: Colors.dark.slate700,
    padding: 8,
    borderRadius: 32,
  },
  ownerProp:{
    color:Colors.dark.slate700,
  }
});
