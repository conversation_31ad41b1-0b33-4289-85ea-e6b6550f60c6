/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const Colors = {
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#ECEDEE',
    background: '#151718', 
    slate50: '#f9fafb',
    slate100: '#f3f4f6',
    slate200: '#e5e7eb',
    slate300: '#d1d5db',
    slate400: '#9ca3af',
    slate500: '#6b7280',
    slate600: '#4b5563',
    slate700: '#1e293b',
    slate800: '#0f172a',
    slate900: '#020617',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
  blue:{
    text: '#60a5fa',
    background: '#60a5fa',
    tint: '#60a5fa',
    icon: '#2563eb',
    tabIconDefault: '#60a5fa',
    tabIconSelected: '#60a5fa',
    blueTwo:'#1e3a8a',
  }
};
